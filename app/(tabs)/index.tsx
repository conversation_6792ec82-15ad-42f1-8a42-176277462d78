import { View, Text, StyleSheet } from 'react-native';
import { TopicList } from '@/components/TopicList';

export default function TopicsScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Educational Topics</Text>
      <TopicList />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
    paddingTop: 60,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 20,
  },
});