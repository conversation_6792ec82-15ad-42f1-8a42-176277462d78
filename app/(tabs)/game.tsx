import { View, Text, StyleSheet } from 'react-native';
import { Link } from 'expo-router';
import { BookOpen } from 'lucide-react-native';
import { WordSearchGame } from '@/components/WordSearchGame';

export default function GameScreen() {
  // TODO: Replace with actual game state management
  const hasSelectedTopic = false; // This should come from your state management

  if (!hasSelectedTopic) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Word Search</Text>
        <View style={styles.noTopicContainer}>
          <BookOpen size={64} color="#9CA3AF" />
          <Text style={styles.noTopicTitle}>No Topic Selected</Text>
          <Text style={styles.noTopicText}>
            Choose an educational topic to start playing!
          </Text>
          <Link href="/" style={styles.selectTopicButton}>
            <Text style={styles.selectTopicButtonText}>Select a Topic</Text>
          </Link>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Word Search</Text>
      <WordSearchGame />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
    paddingTop: 60,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 20,
  },
  noTopicContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  noTopicTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#374151',
    marginTop: 20,
    marginBottom: 12,
  },
  noTopicText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  selectTopicButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  selectTopicButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});