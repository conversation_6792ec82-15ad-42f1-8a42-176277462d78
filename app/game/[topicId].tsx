import { View, Text, StyleSheet } from 'react-native';
import { useLocalSearchParams, Stack } from 'expo-router';
import { WordSearchGame } from '@/components/WordSearchGame';

export default function GameScreen() {
  const { topicId } = useLocalSearchParams<{ topicId: string }>();

  return (
    <>
      <Stack.Screen 
        options={{ 
          title: 'Word Search',
          headerShown: true,
          headerBackTitle: 'Topics'
        }} 
      />
      <View style={styles.container}>
        <WordSearchGame topicId={topicId} />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
    paddingHorizontal: 20,
    paddingTop: 20,
  },
});