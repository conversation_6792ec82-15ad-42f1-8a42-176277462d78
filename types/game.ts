export interface Topic {
  id: string;
  title: string;
  summary: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  wordsToFind: string[];
  completed: boolean;
}

export interface WordSearchGrid {
  grid: string[][];
  size: number;
  words: PlacedWord[];
}

export interface PlacedWord {
  word: string;
  startRow: number;
  startCol: number;
  direction: 'horizontal' | 'vertical' | 'diagonal';
  found: boolean;
}

export interface GameState {
  currentTopic: Topic | null;
  grid: WordSearchGrid | null;
  selectedCells: { row: number; col: number }[];
  foundWords: string[];
  gameStartTime: number | null;
  gameEndTime: number | null;
}

export interface UserProgress {
  completedTopics: string[];
  totalScore: number;
  achievements: string[];
  statistics: {
    totalGamesPlayed: number;
    averageTime: number;
    bestTime: number;
  };
}