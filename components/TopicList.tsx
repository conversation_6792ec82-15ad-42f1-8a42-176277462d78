import { View, Text, StyleSheet, FlatList } from 'react-native';
import { Topic } from '@/types/game';
import { TopicCard } from './TopicCard';

// Mock data - you'll replace this with your actual data
const mockTopics: Topic[] = [
  {
    id: '1',
    title: 'Solar System',
    summary: 'Learn about planets, moons, and celestial bodies...',
    difficulty: 'easy',
    category: 'Science',
    wordsToFind: ['EARTH', 'MARS', 'JUPITER', 'SATURN'],
    completed: false,
  },
  // Add more topics as needed
];

export function TopicList() {
  // TODO: Replace with actual game state management
  const getGameStatus = (topicId: string) => {
    // Mock data - replace with actual game state
    return {
      hasInProgressGame: topicId === '1', // Mock: first topic has in-progress game
      lastPlayedAt: topicId === '1' ? new Date() : undefined,
    };
  };

  return (
    <View style={styles.container}>
      <Text style={styles.subtitle}>
        Each topic includes educational content + word search puzzle
      </Text>
      <FlatList
        data={mockTopics}
        renderItem={({ item }) => {
          const gameStatus = getGameStatus(item.id);
          return (
            <TopicCard 
              topic={item} 
              hasInProgressGame={gameStatus.hasInProgressGame}
              lastPlayedAt={gameStatus.lastPlayedAt}
            />
          );
        }}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 20,
  },
});