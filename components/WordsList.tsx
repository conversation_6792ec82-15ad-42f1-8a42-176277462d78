import { View, Text, StyleSheet } from 'react-native';

export function WordsList() {
  const mockWords = ['EARTH', 'MARS', 'JUPITER', 'SATURN', 'VENUS'];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Words to Find</Text>
      <View style={styles.wordsGrid}>
        {mockWords.map((word, index) => (
          <View key={index} style={styles.wordChip}>
            <Text style={styles.wordText}>{word}</Text>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  wordsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  wordChip: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  wordText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
});