import { View, Text, StyleSheet } from 'react-native';

export function ProgressView() {
  return (
    <View style={styles.container}>
      <Text style={styles.placeholder}>Progress View Component</Text>
      <Text style={styles.note}>
        TODO: Implement user progress tracking, statistics, and achievements
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholder: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  note: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
});