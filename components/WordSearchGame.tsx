import { View, Text, StyleSheet } from 'react-native';
import { WordSearchGrid } from './WordSearchGrid';
import { WordsList } from './WordsList';

interface WordSearchGameProps {
  topicId?: string;
}

export function WordSearchGame({ topicId }: WordSearchGameProps) {
  // TODO: Load topic data and game state based on topicId
  
  return (
    <View style={styles.container}>
      <Text style={styles.topicInfo}>
        Topic ID: {topicId} {/* TODO: Replace with actual topic title */}
      </Text>
      <Text style={styles.instructions}>
        Find all the words hidden in the grid
      </Text>
      <WordSearchGrid />
      <WordsList />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  topicInfo: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  instructions: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 20,
  },
});