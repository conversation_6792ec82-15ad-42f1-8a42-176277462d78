import { View, Text, StyleSheet } from 'react-native';

export function WordSearchGrid() {
  return (
    <View style={styles.container}>
      <Text style={styles.placeholder}>Word Search Grid Component</Text>
      <Text style={styles.note}>
        TODO: Implement interactive grid with letter tiles
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    minHeight: 300,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholder: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  note: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
});