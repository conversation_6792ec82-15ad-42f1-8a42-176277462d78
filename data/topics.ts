import { Topic } from '@/types/game';

// Sample topics - replace with your actual educational content
export const topics: Topic[] = [
  {
    id: '1',
    title: 'Solar System',
    summary: 'Explore the wonders of our solar system, including the eight planets, their moons, and other celestial bodies. Learn about the unique characteristics of each planet, from Mercury\'s extreme temperatures to Neptune\'s powerful winds. Discover how planets orbit the Sun and what makes each one special in our cosmic neighborhood.',
    difficulty: 'easy',
    category: 'Science',
    wordsToFind: ['EARTH', 'MARS', 'JUPITER', 'SATURN', 'VENUS', 'MERCURY', 'NEPTUNE', 'URANUS', 'SUN', 'MOON'],
    completed: false,
  },
  {
    id: '2',
    title: 'Ancient Egypt',
    summary: 'Journey back in time to ancient Egypt, one of history\'s greatest civilizations. Learn about the pharaohs who ruled this mighty empire, the construction of the magnificent pyramids, and the importance of the Nile River. Discover the secrets of mummification, hieroglyphic writing, and the rich culture that flourished for over 3,000 years.',
    difficulty: 'medium',
    category: 'History',
    wordsToFind: ['PYRAMID', 'PHARAOH', 'MUMMY', 'NILE', 'SPHINX', 'PAPYRUS', 'HIEROGLYPH', 'TOMB', 'EGYPT', 'CAIRO'],
    completed: false,
  },
  // TODO: Add more topics with educational content
];